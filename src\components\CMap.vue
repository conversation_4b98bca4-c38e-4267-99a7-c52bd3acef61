<!--  中间背景图片部分 -->
<template>
  <div class="background-image" id="background-image">
    <div class="background-content">
      <img src="@/assets/img/backGroud.png" alt="背景图片" />
    </div>
  </div>
</template>

<script setup lang="ts">
// 不再需要地图相关的导入和逻辑
</script>

<style lang="scss" scoped>
.background-image {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .background-content {
    position: relative;
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
</style>
