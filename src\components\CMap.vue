<!--  中间背景图片部分 -->
<template>
  <div class="background-image" id="background-image">
    <div class="background-content">
      <img src="@/assets/img/backGroud.png" alt="背景图片" />
    </div>
  </div>
</template>

<script setup lang="ts">
// 不再需要地图相关的导入和逻辑
</script>

<style lang="scss" scoped>
.background-image {
  position: absolute;
  top: 120px; /* 避免遮挡顶部标题 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1; /* 确保在背景层但不遮挡其他组件 */
  .background-content {
    position: relative;
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover; /* 改为 cover 以填满整个区域 */
    }
  }
}
</style>
