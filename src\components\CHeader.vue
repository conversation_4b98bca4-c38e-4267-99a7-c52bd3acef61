<!-- 顶部标题 -->
<template>
  <header class="header"></header>
  <!-- 图片展示区域 -->
  <div class="image-display">
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="园区总能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">园区总能耗</span>
        <span class="value">1000 kwh</span>
      </div>
    </div>
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="单位面积能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">单位面积能耗</span>
        <span class="value">100 kwh/m³</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 静态图片展示组件，无需额外逻辑
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  top: 12px;
  width: calc(100% - 24px);
  height: 87px;
  z-index: 20; /* 确保显示在背景图片之上 */
  background: url('@/assets/img/head_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
}

.image-display {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20; /* 确保显示在背景图片之上 */
  display: flex;
  gap: 100px;
  align-items: center;
  justify-content: center;
}

.display-item {
  display: flex;
  align-items: center;
  position: relative;
  width: 350px;
  height: 100px;
  padding-left: 10px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.icon {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/img/sd_bottom.png') no-repeat center center;
  background-size: 100% 100%;
  gap: 3px;
  color: #fff;
  flex: 1;
  margin-left: -13px;
}

.label {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.value {
  font-size: 24px;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}
</style>
